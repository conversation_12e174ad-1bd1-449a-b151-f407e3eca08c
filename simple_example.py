#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的 KKApp 加密解密示例
直接使用你提供的数据格式
"""

from kkapp_python_wrapper import KKAppCrypto
import time

# 你提供的原始数据
original_data = {
    'bookid': '1913650320397009853',
    'imei': '4075184199',
    'kouling': '',
    'book': '1850231837869400064',
    'time': str(int(time.time() * 1000))
}

print("=== 简单加密解密示例 ===")
print(f"原始数据: {original_data}")
print()

# 创建加密工具实例
crypto = KKAppCrypto()
encrypted_data = crypto.encrypt_data(original_data)
print(encrypted_data)


import requests

headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'dnt': '1',
    'origin': 'https://kkvvastv.uk',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://kkvvastv.uk/read/?book=1850231837869400064&bookid=1913650320397009853&imei=4075184199&kouling=',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
}

json_data = {
    'endata': encrypted_data,
}

response = requests.post('https://kkvvastv.uk/api/content.php', headers=headers, json=json_data)

print(response.text)

decrypted = crypto.decrypt_data(response.text)
print(f"解密结果: {decrypted}")