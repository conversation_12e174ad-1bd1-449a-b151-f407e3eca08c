#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KKApp 加密解密使用示例
针对用户提供的数据格式进行加密和解密
"""

from kkapp_python_wrapper import KKAppCrypto


def encrypt_user_data():
    """加密用户提供的数据"""
    print("=== 加密用户数据 ===")
    
    # 用户提供的原始数据
    user_data = {
        'bookid': '1913650320397009853',
        'imei': '4075184199',
        'kouling': '',
        'book': '1850231837869400064',
        'time': 1754623806867
    }
    
    print(f"原始数据: {user_data}")
    
    # 初始化加密工具
    crypto = KKAppCrypto()
    
    # 加密数据
    encrypted_result = crypto.encrypt_data(user_data)
    
    if encrypted_result:
        print(f"✅ 加密成功!")
        print(f"加密结果: {encrypted_result}")
        return encrypted_result
    else:
        print("❌ 加密失败!")
        return None


def decrypt_data(encrypted_data):
    """解密数据"""
    print("\n=== 解密数据 ===")
    
    if not encrypted_data:
        print("❌ 没有加密数据可解密")
        return None
    
    print(f"加密数据: {encrypted_data}")
    
    # 初始化加密工具
    crypto = KKAppCrypto()
    
    # 解密数据
    decrypted_result = crypto.decrypt_data(encrypted_data)
    
    if decrypted_result:
        print(f"✅ 解密成功!")
        print(f"解密结果: {decrypted_result}")
        return decrypted_result
    else:
        print("❌ 解密失败!")
        return None


def create_request_for_user_data():
    """使用便捷函数为用户数据创建请求"""
    print("\n=== 使用便捷函数创建请求 ===")
    
    # 初始化加密工具
    crypto = KKAppCrypto()
    
    # 使用便捷函数创建请求数据（会自动添加当前时间戳）
    request_data = crypto.create_request_data(
        bookid='1913650320397009853',
        kouling='',  # 空口令
        imei='4075184199',
        book='1850231837869400064'
    )
    
    if request_data:
        print(f"✅ 创建请求成功!")
        print(f"请求数据: {request_data}")
        return request_data
    else:
        print("❌ 创建请求失败!")
        return None


def main():
    """主函数 - 完整的加密解密流程演示"""
    print("=== KKApp 加密解密完整流程演示 ===\n")
    
    # 1. 加密用户数据
    encrypted = encrypt_user_data()
    
    # 2. 解密数据
    if encrypted:
        decrypted = decrypt_data(encrypted)
        
        # 验证数据完整性
        if decrypted:
            original_data = {
                'bookid': '1913650320397009853',
                'imei': '4075184199',
                'kouling': '',
                'book': '1850231837869400064',
                'time': 1754623806867
            }
            
            print(f"\n数据完整性检查: {'✅ 通过' if decrypted == original_data else '❌ 失败'}")
    
    # 3. 使用便捷函数创建请求
    create_request_for_user_data()
    
    print("\n=== 演示完成 ===")
    
    # 使用说明
    print("\n=== 使用说明 ===")
    print("1. 确保已安装 Node.js 和 crypto-js 包")
    print("2. 确保 kkapp_crypto.js 文件在当前目录")
    print("3. 导入 KKAppCrypto 类:")
    print("   from kkapp_python_wrapper import KKAppCrypto")
    print("4. 创建实例: crypto = KKAppCrypto()")
    print("5. 加密: encrypted = crypto.encrypt_data(your_data)")
    print("6. 解密: decrypted = crypto.decrypt_data(encrypted_data)")
    print("7. 创建请求: request = crypto.create_request_data(bookid, kouling, imei, book)")


if __name__ == "__main__":
    main()
