// KKApp 加密解密工具
// 使用找到的正确密钥: 97a88a411bf5f3b4bk21c7a7i107583c

const CryptoJS = require('crypto-js');

// 正确的密钥
const SECRET_KEY = '97a88a411bf5f3b4bk21c7a7i107583c';

/**
 * 加密数据
 * @param {Object} data - 要加密的数据对象
 * @returns {string} - Base64编码的加密结果
 */
function encryptData(data) {
    try {
        // 将数据转换为JSON字符串
        const jsonString = JSON.stringify(data);
        
        // 解析密钥
        const key = CryptoJS.enc.Utf8.parse(SECRET_KEY);
        
        // 生成随机IV (16字节)
        const iv = CryptoJS.lib.WordArray.random(16);
        
        // AES加密
        const encrypted = CryptoJS.AES.encrypt(jsonString, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });
        
        // 将IV和密文合并，然后转换为Base64
        const result = iv.concat(encrypted.ciphertext).toString(CryptoJS.enc.Base64);
        
        console.log('✅ 加密成功');
        console.log('原始数据:', data);
        console.log('加密结果:', result);
        
        return result;
    } catch (error) {
        console.error('❌ 加密失败:', error);
        return null;
    }
}

/**
 * 解密数据
 * @param {string} encryptedData - Base64编码的加密数据
 * @returns {Object|null} - 解密后的数据对象
 */
function decryptData(encryptedData) {
    try {
        // 解析密钥
        const key = CryptoJS.enc.Utf8.parse(SECRET_KEY);
        
        // 解析加密数据
        const encrypted = CryptoJS.enc.Base64.parse(encryptedData);
        
        // 提取IV (前16字节)
        const iv = CryptoJS.lib.WordArray.create(encrypted.words.slice(0, 4));
        iv.sigBytes = 16;
        
        // 提取密文 (剩余字节)
        const ciphertext = CryptoJS.lib.WordArray.create(encrypted.words.slice(4));
        ciphertext.sigBytes = encrypted.sigBytes - 16;
        
        // AES解密
        const decrypted = CryptoJS.AES.decrypt(
            { ciphertext: ciphertext },
            key,
            {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            }
        );
        
        // 转换为UTF-8字符串
        const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);
        
        if (!decryptedString) {
            throw new Error('解密结果为空');
        }
        
        // 解析JSON
        const result = JSON.parse(decryptedString);
        
        console.log('✅ 解密成功');
        console.log('加密数据:', encryptedData);
        console.log('解密结果:', result);
        
        return result;
    } catch (error) {
        console.error('❌ 解密失败:', error);
        return null;
    }
}

/**
 * 创建请求数据 - 方便快速创建加密数据
 * @param {string} bookid - 书籍ID
 * @param {string} kouling - 口令
 * @param {string} imei - 设备IMEI (可选)
 * @param {string} book - 书籍编号 (可选)
 * @returns {string} - 加密后的数据
 */
function createRequestData(bookid, kouling, imei = '', book = '') {
    const data = {
        bookid: bookid,
        kouling: kouling,
        imei: imei,
        book: book,
        time: Date.now()
    };
    
    return encryptData(data);
}

// 导出函数
module.exports = {
    encryptData,
    decryptData,
    createRequestData,
    SECRET_KEY
};

// 如果直接运行此文件，执行测试
if (require.main === module) {
    console.log('=== KKApp 加密解密测试 ===\n');
    
    // 测试1: 加密自定义数据
    console.log('📝 测试1: 加密自定义数据');
    const testData = {
        bookid: "1913650320397009853",
        kouling: "test123",
        imei: "4075184199",
        book: "1850231837869400064",
        time: Date.now()
    };
    
    const encrypted = encryptData(testData);
    console.log('\n');
    
    // 测试2: 解密服务器返回的数据
    console.log('📝 测试2: 解密服务器返回的数据');
    const serverData = "iO9p0DNDNFyDLpnlo797WWybvuogzeRQRAqzOFvCNSq97JmJmI9FjQWgAWEvIWlm4G2xpb7I3DbzL9SOPZ+UmB8adCNoC54fVe9ymNaaGZj+1sUfsKsepRURUfgUvvP1mQynMV18LBt2gUmRLQTxn8Zv/Ch3uwHlc1yurQebEQp8XCCwPvqjcDssTXls+YU8";
    
    const decrypted = decryptData(serverData);
    console.log('\n');
    
    // 测试3: 使用便捷函数创建请求数据
    console.log('📝 测试3: 使用便捷函数创建请求数据');
    const requestData = createRequestData("123456789", "mypassword", "1234567890", "book001");
    console.log('\n');
    
    console.log('=== 测试完成 ===');
}
