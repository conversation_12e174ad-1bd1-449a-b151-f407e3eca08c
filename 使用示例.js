// KKApp 加密解密使用示例

const { encryptData, decryptData, createRequestData } = require('./kkapp_crypto');

console.log('=== KKApp 加密解密使用示例 ===\n');

// 示例1: 快速创建请求数据
console.log('📝 示例1: 快速创建请求数据');
const requestData = createRequestData(
    "1913650320397009853",  // bookid
    "mypassword",           // kouling
    "4075184199",          // imei (可选)
    "1850231837869400064"  // book (可选)
);
console.log('生成的加密请求数据:', requestData);
console.log('\n');

// 示例2: 手动加密自定义数据
console.log('📝 示例2: 手动加密自定义数据');
const customData = {
    bookid: "123456789",
    kouling: "test123",
    imei: "987654321",
    book: "book001",
    custom_field: "额外的字段",
    time: Date.now()
};

const encrypted = encryptData(customData);
console.log('\n');

// 示例3: 解密服务器返回的数据
console.log('📝 示例3: 解密服务器返回的数据');
const serverResponse = "iO9p0DNDNFyDLpnlo797WWybvuogzeRQRAqzOFvCNSq97JmJmI9FjQWgAWEvIWlm4G2xpb7I3DbzL9SOPZ+UmB8adCNoC54fVe9ymNaaGZj+1sUfsKsepRURUfgUvvP1mQynMV18LBt2gUmRLQTxn8Zv/Ch3uwHlc1yurQebEQp8XCCwPvqjcDssTXls+YU8";

const decrypted = decryptData(serverResponse);
console.log('\n');

// 示例4: 完整的加密-解密流程
console.log('📝 示例4: 完整的加密-解密流程');
const originalData = {
    action: "get_book_content",
    bookid: "999888777",
    kouling: "secret123",
    page: 1
};

console.log('原始数据:', originalData);

// 加密
const encryptedData = encryptData(originalData);
if (encryptedData) {
    console.log('\n加密后发送给服务器的数据:', encryptedData);
    
    // 解密（模拟服务器返回相同数据）
    const decryptedData = decryptData(encryptedData);
    if (decryptedData) {
        console.log('\n解密后的数据:', decryptedData);
        console.log('数据完整性检查:', JSON.stringify(originalData) === JSON.stringify(decryptedData) ? '✅ 通过' : '❌ 失败');
    }
}

console.log('\n=== 使用提示 ===');
console.log('1. 使用 createRequestData() 快速创建标准请求数据');
console.log('2. 使用 encryptData() 加密任意JSON对象');
console.log('3. 使用 decryptData() 解密服务器返回的数据');
console.log('4. 密钥已内置，无需手动设置');
console.log('5. 支持Node.js和浏览器环境');

console.log('\n=== 示例完成 ===');
