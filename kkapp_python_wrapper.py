#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KKApp 加密解密 Python 包装器
通过调用 Node.js 脚本来实现加密和解密功能
"""

import json
import subprocess
import os
import tempfile
from typing import Dict, Any, Optional


class KKAppCrypto:
    """KKApp 加密解密工具的 Python 包装器"""
    
    def __init__(self, js_script_path: str = "kkapp_crypto.js"):
        """
        初始化 KKApp 加密解密工具
        
        Args:
            js_script_path: JavaScript 脚本文件路径
        """
        self.js_script_path = js_script_path
        self._check_node_js()
        self._check_js_file()
    
    def _check_node_js(self):
        """检查 Node.js 是否可用"""
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                raise RuntimeError("Node.js 不可用")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            raise RuntimeError("Node.js 未安装或不在 PATH 中")
    
    def _check_js_file(self):
        """检查 JavaScript 文件是否存在"""
        if not os.path.exists(self.js_script_path):
            raise FileNotFoundError(f"JavaScript 文件不存在: {self.js_script_path}")
    
    def _run_js_function(self, function_name: str, data: Any = None) -> Optional[str]:
        """
        运行 JavaScript 函数
        
        Args:
            function_name: 要调用的函数名
            data: 传递给函数的数据
            
        Returns:
            函数执行结果
        """
        # 创建临时的 JavaScript 文件来执行特定函数
        js_code = f"""
const {{ encryptData, decryptData, createRequestData }} = require('./{self.js_script_path}');

function main() {{
    try {{
        let result;
        
        if ('{function_name}' === 'encrypt') {{
            const data = {json.dumps(data) if data else 'null'};
            result = encryptData(data);
        }} else if ('{function_name}' === 'decrypt') {{
            const encryptedData = {json.dumps(data) if data else 'null'};
            result = decryptData(encryptedData);
        }} else if ('{function_name}' === 'createRequest') {{
            const params = {json.dumps(data) if data else 'null'};
            result = createRequestData(
                params.bookid,
                params.kouling || '',
                params.imei || '',
                params.book || ''
            );
        }}
        
        if (result !== null && result !== undefined) {{
            console.log('RESULT_START');
            console.log(JSON.stringify(result));
            console.log('RESULT_END');
        }} else {{
            console.error('函数执行失败');
            process.exit(1);
        }}
    }} catch (error) {{
        console.error('执行错误:', error.message);
        process.exit(1);
    }}
}}

main();
"""
        
        # 写入临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False, encoding='utf-8') as f:
            f.write(js_code)
            temp_js_file = f.name
        
        try:
            # 执行 Node.js 脚本
            result = subprocess.run(
                ['node', temp_js_file],
                capture_output=True,
                text=True,
                timeout=30,
                cwd=os.path.dirname(os.path.abspath(self.js_script_path))
            )
            
            if result.returncode != 0:
                raise RuntimeError(f"JavaScript 执行失败: {result.stderr}")
            
            # 解析输出
            output_lines = result.stdout.strip().split('\n')
            result_start_idx = -1
            result_end_idx = -1
            
            for i, line in enumerate(output_lines):
                if line.strip() == 'RESULT_START':
                    result_start_idx = i
                elif line.strip() == 'RESULT_END':
                    result_end_idx = i
                    break
            
            if result_start_idx >= 0 and result_end_idx > result_start_idx:
                result_json = '\n'.join(output_lines[result_start_idx + 1:result_end_idx])
                return json.loads(result_json)
            else:
                raise RuntimeError("无法解析 JavaScript 函数返回结果")
                
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_js_file)
            except:
                pass
    
    def encrypt_data(self, data: Dict[str, Any]) -> Optional[str]:
        """
        加密数据
        
        Args:
            data: 要加密的数据字典
            
        Returns:
            Base64 编码的加密结果，失败返回 None
        """
        return self._run_js_function('encrypt', data)
    
    def decrypt_data(self, encrypted_data: str) -> Optional[Dict[str, Any]]:
        """
        解密数据
        
        Args:
            encrypted_data: Base64 编码的加密数据
            
        Returns:
            解密后的数据字典，失败返回 None
        """
        return self._run_js_function('decrypt', encrypted_data)
    
    def create_request_data(self, bookid: str, kouling: str = '', 
                          imei: str = '', book: str = '') -> Optional[str]:
        """
        创建请求数据（自动添加时间戳并加密）
        
        Args:
            bookid: 书籍ID
            kouling: 口令
            imei: 设备IMEI
            book: 书籍编号
            
        Returns:
            加密后的请求数据，失败返回 None
        """
        params = {
            'bookid': bookid,
            'kouling': kouling,
            'imei': imei,
            'book': book
        }
        return self._run_js_function('createRequest', params)


def main():
    """示例用法"""
    print("=== KKApp Python 包装器示例 ===\n")
    
    # 初始化加密工具
    crypto = KKAppCrypto()
    
    # 示例数据
    test_data = {
        'bookid': '1913650320397009853',
        'imei': '4075184199',
        'kouling': '',
        'book': '1850231837869400064',
        'time': 1754623806867
    }
    
    print("📝 示例1: 加密数据")
    print(f"原始数据: {test_data}")
    
    # 加密数据
    encrypted = crypto.encrypt_data(test_data)
    if encrypted:
        print(f"加密结果: {encrypted}")
        print()
        
        # 解密数据
        print("📝 示例2: 解密数据")
        decrypted = crypto.decrypt_data(encrypted)
        if decrypted:
            print(f"解密结果: {decrypted}")
            print(f"数据完整性: {'✅ 通过' if decrypted == test_data else '❌ 失败'}")
        else:
            print("❌ 解密失败")
    else:
        print("❌ 加密失败")
    
    print()
    
    # 使用便捷函数创建请求数据
    print("📝 示例3: 创建请求数据")
    request_data = crypto.create_request_data(
        bookid='1913650320397009853',
        kouling='',
        imei='4075184199',
        book='1850231837869400064'
    )
    
    if request_data:
        print(f"生成的请求数据: {request_data}")
    else:
        print("❌ 创建请求数据失败")
    
    print("\n=== 示例完成 ===")


if __name__ == "__main__":
    main()
