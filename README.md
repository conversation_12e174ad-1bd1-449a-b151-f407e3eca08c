# KKApp 加密解密 Python 包装器

这是一个 Python 包装器，用于调用 JavaScript 实现的 KKApp 加密解密功能。

## 环境要求

1. **Node.js**: 需要安装 Node.js (建议版本 14 或更高)
2. **crypto-js**: JavaScript 加密库

## 安装步骤

### 1. 安装 Node.js
- 访问 [Node.js 官网](https://nodejs.org/) 下载并安装
- 或使用包管理器安装 (如 Windows 上的 Chocolatey, macOS 上的 Homebrew)

### 2. 安装 crypto-js 包
在项目目录中运行:
```bash
npm install crypto-js
```

### 3. 验证安装
运行以下命令验证 Node.js 是否正确安装:
```bash
node --version
```

## 文件说明

- `kkapp_crypto.js` - JavaScript 加密解密核心实现
- `main.js` - JavaScript 使用示例
- `kkapp_python_wrapper.py` - Python 包装器类
- `example_usage.py` - Python 使用示例

## 使用方法

### 基本用法

```python
from kkapp_python_wrapper import KKAppCrypto

# 初始化
crypto = KKAppCrypto()

# 准备数据
data = {
    'bookid': '1913650320397009853',
    'imei': '4075184199',
    'kouling': '',
    'book': '1850231837869400064',
    'time': 1754623806867
}

# 加密
encrypted = crypto.encrypt_data(data)
print(f"加密结果: {encrypted}")

# 解密
decrypted = crypto.decrypt_data(encrypted)
print(f"解密结果: {decrypted}")
```

### 便捷函数

```python
# 快速创建请求数据（自动添加时间戳）
request_data = crypto.create_request_data(
    bookid='1913650320397009853',
    kouling='',
    imei='4075184199',
    book='1850231837869400064'
)
```

## 运行示例

```bash
# 运行 Python 示例
python example_usage.py

# 运行 JavaScript 示例
node main.js
```

## API 说明

### KKAppCrypto 类

#### `__init__(js_script_path="kkapp_crypto.js")`
初始化加密工具，指定 JavaScript 脚本路径。

#### `encrypt_data(data: Dict[str, Any]) -> Optional[str]`
加密数据字典，返回 Base64 编码的加密字符串。

#### `decrypt_data(encrypted_data: str) -> Optional[Dict[str, Any]]`
解密 Base64 编码的加密数据，返回原始数据字典。

#### `create_request_data(bookid: str, kouling: str = '', imei: str = '', book: str = '') -> Optional[str]`
创建请求数据，自动添加时间戳并加密。

## 注意事项

1. 确保 `kkapp_crypto.js` 文件在正确的路径
2. 确保 Node.js 在系统 PATH 中
3. 确保已安装 crypto-js 包
4. 加密使用的密钥已内置在 JavaScript 代码中

## 错误处理

- 如果 Node.js 不可用，会抛出 `RuntimeError`
- 如果 JavaScript 文件不存在，会抛出 `FileNotFoundError`
- 如果加密/解密失败，函数会返回 `None`

## 示例数据格式

```python
{
    'bookid': '1913650320397009853',
    'imei': '4075184199',
    'kouling': '',
    'book': '1850231837869400064',
    'time': 1754623806867
}
```
