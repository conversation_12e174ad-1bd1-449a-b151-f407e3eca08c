# KKApp 加密解密 Python 使用指南

## 快速开始

### 1. 基本使用

```python
from kkapp_python_wrapper import KKAppCrypto

# 创建加密工具实例
crypto = KKAppCrypto()

# 你的数据
data = {
    'bookid': '1913650320397009853',
    'imei': '4075184199',
    'kouling': '',
    'book': '1850231837869400064',
    'time': 1754623806867
}

# 加密
encrypted = crypto.encrypt_data(data)
print(f"加密结果: {encrypted}")

# 解密
decrypted = crypto.decrypt_data(encrypted)
print(f"解密结果: {decrypted}")
```

### 2. 便捷函数使用

```python
# 快速创建请求数据（自动添加时间戳）
request_data = crypto.create_request_data(
    bookid='1913650320397009853',
    kouling='',
    imei='4075184199',
    book='1850231837869400064'
)
print(f"请求数据: {request_data}")
```

## 运行示例

```bash
# 运行简单示例
python simple_example.py

# 运行完整示例
python example_usage.py

# 运行包装器自带的测试
python kkapp_python_wrapper.py
```

## API 参考

### KKAppCrypto 类

#### 初始化
```python
crypto = KKAppCrypto(js_script_path="kkapp_crypto.js")
```

#### 方法

**encrypt_data(data: dict) -> str**
- 加密数据字典
- 返回 Base64 编码的加密字符串
- 失败返回 None

**decrypt_data(encrypted_data: str) -> dict**
- 解密 Base64 编码的加密数据
- 返回原始数据字典
- 失败返回 None

**create_request_data(bookid, kouling='', imei='', book='') -> str**
- 创建请求数据，自动添加时间戳并加密
- 返回加密后的请求数据
- 失败返回 None

## 数据格式

### 输入数据格式
```python
{
    'bookid': '1913650320397009853',
    'imei': '4075184199',
    'kouling': '',
    'book': '1850231837869400064',
    'time': 1754623806867
}
```

### 加密输出格式
```
Base64 编码的字符串，例如：
"glt91h01x0axeHeXkaNlHpPgbxgzer60E5HQ95o1BhIoSU/f0sIVFl58a08dtxiczqIwAzYQXaqLmouRHKXkKavt8y2gRxQnJeEnbE5Q0quZZdyO5JfT1f2inkGn1+77RZU7gs8qUU6UVwakw4JnOJcnpQYerdFza1WMJiErXlr4QFwVLyXAxNX1Z4DQS5dTC"
```

## 错误处理

所有方法在失败时返回 `None`，你可以这样处理：

```python
encrypted = crypto.encrypt_data(data)
if encrypted:
    print("加密成功")
else:
    print("加密失败")
```

## 注意事项

1. 确保 Node.js 已安装并在 PATH 中
2. 确保 crypto-js 包已安装 (`npm install crypto-js`)
3. 确保 `kkapp_crypto.js` 文件在正确位置
4. 密钥已内置在 JavaScript 代码中，无需额外配置

## 故障排除

### 常见错误

1. **"Node.js 未安装"**
   - 安装 Node.js: https://nodejs.org/

2. **"Cannot find module 'crypto-js'"**
   - 运行: `npm install crypto-js`

3. **"JavaScript 文件不存在"**
   - 确保 `kkapp_crypto.js` 在当前目录或指定路径

4. **编码错误**
   - 已在代码中处理，使用 UTF-8 编码

## 完整工作流程

```python
from kkapp_python_wrapper import KKAppCrypto

def encrypt_and_send_data():
    crypto = KKAppCrypto()
    
    # 准备数据
    data = {
        'bookid': '1913650320397009853',
        'imei': '4075184199',
        'kouling': '',
        'book': '1850231837869400064',
        'time': 1754623806867
    }
    
    # 加密
    encrypted = crypto.encrypt_data(data)
    if encrypted:
        # 发送到服务器
        # response = send_to_server(encrypted)
        
        # 解密服务器响应
        # decrypted = crypto.decrypt_data(response)
        return encrypted
    else:
        print("加密失败")
        return None
```
